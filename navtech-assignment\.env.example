# NavTech Resume Parser - Environment Variables Example
# Copy this file to .env and add your actual API keys

# =============================================================================
# REQUIRED API KEYS
# =============================================================================

# Google Gemini API Key
# Get your free API key from: https://makersuite.google.com/app/apikey
# Note: Free tier has quota limits
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Key (Optional)
# Get from: https://platform.openai.com/api-keys
# Note: Requires paid account for API access
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API Key (Recommended - Free DeepSeek R1 model)
# Get your free API key from: https://openrouter.ai/keys
# This provides access to DeepSeek R1 model for free
OPENROUTER_API_KEY=your_openrouter_api_key_here

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Default LLM Provider (smart_transformer is recommended for best balance)
# Options: smart_transformer, layoutlm_transformer, gemini, openrouter, openai
DEFAULT_LLM_PROVIDER=smart_transformer

# Model configurations (usually don't need to change these)
GEMINI_MODEL=gemini-1.5-pro
OPENAI_MODEL=gpt-3.5-turbo
OPENROUTER_MODEL=deepseek/deepseek-r1-0528-qwen3-8b:free

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env:
#    cp .env.example .env

# 2. Get API keys from the URLs above

# 3. Replace the placeholder values with your actual API keys

# 4. For testing, you can use these working API keys (provided by developer):
#    GEMINI_API_KEY=AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ (quota exceeded)
#    OPENROUTER_API_KEY=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

# 5. Run the application:
#    python app.py

# =============================================================================
# MODEL RECOMMENDATIONS
# =============================================================================

# For best results, use this priority order:
# 1. smart_transformer (Default) - Fast, accurate, works offline
# 2. openrouter (DeepSeek R1) - Highest accuracy, requires internet
# 3. layoutlm_transformer - Fastest, good accuracy, works offline
# 4. gemini - Good accuracy, requires internet and API quota

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you get API errors:
# - Check that your API keys are valid and not expired
# - Ensure you have sufficient quota/credits
# - For OpenRouter, make sure you're using the correct model name
# - Local transformer models (smart_transformer, layoutlm_transformer) work without API keys

# For support, check the README.md file or contact the developer
