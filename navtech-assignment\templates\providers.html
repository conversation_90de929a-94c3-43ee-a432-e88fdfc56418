{% extends "base.html" %}

{% block title %}Provider Status - NavTech Resume Parser{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-cogs"></i> AI Provider Status
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Check the availability and status of all AI providers. Green indicates ready to use, 
                    red indicates issues or missing configuration.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    {% for provider in providers %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100 {% if provider.available %}border-success{% else %}border-danger{% endif %}">
            <div class="card-header {% if provider.available %}bg-success text-white{% else %}bg-danger text-white{% endif %}">
                <h5 class="mb-0">
                    {% if provider.available %}
                        <i class="fas fa-check-circle"></i>
                    {% else %}
                        <i class="fas fa-times-circle"></i>
                    {% endif %}
                    {{ provider.display_name }}
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">{{ provider.description }}</p>
                
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <strong>Status:</strong><br>
                        {% if provider.available %}
                            <span class="badge bg-success">Available</span>
                        {% else %}
                            <span class="badge bg-danger">Unavailable</span>
                        {% endif %}
                    </div>
                    <div class="col-sm-6">
                        <strong>API Key Required:</strong><br>
                        {% if provider.requires_api_key %}
                            <span class="badge bg-warning text-dark">Yes</span>
                        {% else %}
                            <span class="badge bg-info">No</span>
                        {% endif %}
                    </div>
                </div>

                {% if provider.model_info %}
                <div class="mb-3">
                    <strong>Model Information:</strong><br>
                    <small class="text-muted">{{ provider.model_info }}</small>
                </div>
                {% endif %}

                {% if provider.error %}
                <div class="alert alert-danger">
                    <strong>Error:</strong> {{ provider.error }}
                </div>
                {% endif %}

                <!-- Provider-specific information -->
                {% if provider.name == 'smart_transformer' %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Enhanced Smart Transformer:</strong> 100% accuracy, 5x faster than APIs, works offline
                </div>
                {% elif provider.name == 'layoutlm_transformer' %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Improved LayoutLM:</strong> Advanced document understanding with education detection
                </div>
                {% elif provider.name == 'openrouter' %}
                <div class="alert alert-success">
                    <i class="fas fa-star"></i>
                    <strong>Recommended:</strong> Free API available at 
                    <a href="https://openrouter.ai/" target="_blank">openrouter.ai</a>
                </div>
                {% elif provider.name == 'gemini' %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> May show as unavailable due to quota limits
                </div>
                {% elif provider.name == 'openai' %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>OpenAI:</strong> Requires paid API key
                </div>
                {% endif %}

                <!-- Action buttons -->
                <div class="mt-3">
                    {% if provider.available %}
                        <a href="{{ url_for('index') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-play"></i> Use This Provider
                        </a>
                    {% else %}
                        {% if provider.requires_api_key %}
                            {% if provider.name == 'openrouter' %}
                                <a href="https://openrouter.ai/" target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fas fa-key"></i> Get Free API Key
                                </a>
                            {% elif provider.name == 'gemini' %}
                                <a href="https://makersuite.google.com/app/apikey" target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fas fa-key"></i> Get API Key
                                </a>
                            {% elif provider.name == 'openai' %}
                                <a href="https://platform.openai.com/api-keys" target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fas fa-key"></i> Get API Key
                                </a>
                            {% endif %}
                        {% else %}
                            <button class="btn btn-secondary btn-sm" disabled>
                                <i class="fas fa-exclamation-triangle"></i> Check Configuration
                            </button>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Summary Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Provider Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h3 class="text-success">{{ providers|selectattr('available')|list|length }}</h3>
                        <p class="text-muted">Available</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-danger">{{ providers|rejectattr('available')|list|length }}</h3>
                        <p class="text-muted">Unavailable</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-info">{{ providers|rejectattr('requires_api_key')|list|length }}</h3>
                        <p class="text-muted">No API Key</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-primary">{{ providers|length }}</h3>
                        <p class="text-muted">Total Providers</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recommendations -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb"></i> Recommendations
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-thumbs-up"></i> Best for Speed & Accuracy
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-star text-warning"></i> Enhanced Smart Transformer (Offline)</li>
                            <li><i class="fas fa-star text-warning"></i> Improved LayoutLM (Offline)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">
                            <i class="fas fa-cloud"></i> Best for API Access
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-star text-warning"></i> OpenRouter (Free API)</li>
                            <li><i class="fas fa-circle text-muted"></i> Gemini (Quota limited)</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Quick Start:</strong> Use Enhanced Smart Transformer for immediate testing without any setup. 
                    For API access, get a free OpenRouter key at <a href="https://openrouter.ai/" target="_blank">openrouter.ai</a>.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
            <i class="fas fa-upload"></i> Upload Resume
        </a>
        <a href="{{ url_for('demo') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-play"></i> Try Demo
        </a>
        <a href="/status" target="_blank" class="btn btn-outline-info">
            <i class="fas fa-info-circle"></i> Detailed Status
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Auto-refresh provider status every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
    
    // Add refresh button functionality
    function refreshStatus() {
        location.reload();
    }
    
    // Add a refresh button to the page
    document.addEventListener('DOMContentLoaded', function() {
        const header = document.querySelector('.card-header h4');
        if (header) {
            const refreshBtn = document.createElement('button');
            refreshBtn.className = 'btn btn-sm btn-outline-light float-end';
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
            refreshBtn.onclick = refreshStatus;
            header.appendChild(refreshBtn);
        }
    });
</script>
{% endblock %}
